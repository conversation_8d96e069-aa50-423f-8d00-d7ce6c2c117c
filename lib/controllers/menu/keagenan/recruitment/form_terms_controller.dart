import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';

class FormTermsController extends BaseControllers {
  final RecruitmentFormController baseController;

  FormTermsController({required this.baseController});

  // Terms and conditions state
  RxBool isAllTermsRead = false.obs;
  RxBool isAgreementChecked = false.obs;
  RxBool isSignatureCompleted = false.obs;
  RxBool isParafCompleted = false.obs;

  // Document viewing state
  RxBool isPkajDocumentViewed = false.obs;
  RxList<bool> termDocumentsViewed = <bool>[].obs;

  // Signature and paraf data
  RxString signatureData = ''.obs;
  RxString parafData = ''.obs;

  // Validation errors
  RxString agreementError = ''.obs;
  RxString signatureError = ''.obs;
  RxString parafError = ''.obs;

  // Share functionality
  RxBool isSharing = false.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize term documents viewed list
    // Adjust the size based on the number of terms documents
    termDocumentsViewed.value = List.filled(5, false); // Assuming 5 documents
  }

  // Check if all terms and conditions have been read
  void checkAllTermsRead() {
    bool allRead =
        isPkajDocumentViewed.value &&
        termDocumentsViewed.every((viewed) => viewed);
    isAllTermsRead.value = allRead;

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Mark PKAJ document as viewed
  void markPkajDocumentViewed() {
    isPkajDocumentViewed.value = true;
    checkAllTermsRead();
  }

  // Mark specific term document as viewed
  void markTermDocumentViewed(int index) {
    if (index >= 0 && index < termDocumentsViewed.length) {
      termDocumentsViewed[index] = true;
      checkAllTermsRead();
    }
  }

  // Toggle agreement checkbox
  void toggleAgreement(bool? value) {
    isAgreementChecked.value = value ?? false;
    agreementError.value = '';

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Set signature data
  void setSignatureData(String data) {
    signatureData.value = data;
    isSignatureCompleted.value = data.isNotEmpty;
    signatureError.value = '';

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Set paraf data
  void setParafData(String data) {
    parafData.value = data;
    isParafCompleted.value = data.isNotEmpty;
    parafError.value = '';

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Clear signature
  void clearSignature() {
    signatureData.value = '';
    isSignatureCompleted.value = false;

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Clear paraf
  void clearParaf() {
    parafData.value = '';
    isParafCompleted.value = false;

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Share form functionality
  Future<void> shareForm() async {
    if (isSharing.value) return;

    isSharing.value = true;

    try {
      // Implement share functionality here
      // This could involve generating a shareable link or PDF
      log('Sharing form with ID: ${baseController.formId.value}');

      // Example implementation:
      // final shareUrl = await generateShareableUrl();
      // await Share.share(shareUrl);

      Get.snackbar(
        'Berhasil',
        'Link formulir berhasil dibagikan',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      log('Error sharing form: $e');
      Get.snackbar(
        'Error',
        'Gagal membagikan formulir',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isSharing.value = false;
    }
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Populate terms and signature data
    signatureData.value = formData.signature ?? '';
    parafData.value = formData.paraf ?? '';

    isSignatureCompleted.value = signatureData.value.isNotEmpty;
    isParafCompleted.value = parafData.value.isNotEmpty;

    // You might want to add fields to RecruitmentFormModel for terms state
    // For now, we'll assume if signature exists, terms were read
    if (signatureData.value.isNotEmpty) {
      isPkajDocumentViewed.value = true;
      termDocumentsViewed.fillRange(0, termDocumentsViewed.length, true);
      isAllTermsRead.value = true;
      isAgreementChecked.value = true;
    }
  }

  // Validate form terms
  bool validateForm() {
    bool isValid = true;

    // Clear previous errors
    agreementError.value = '';
    signatureError.value = '';
    parafError.value = '';

    // Validate that all terms have been read
    if (!isAllTermsRead.value) {
      agreementError.value = 'Mohon baca seluruh dokumen terlebih dahulu';
      isValid = false;
    }

    // Validate agreement checkbox
    if (!isAgreementChecked.value) {
      agreementError.value = 'Anda harus menyetujui perjanjian keagenan';
      isValid = false;
    }

    // Validate signature
    if (signatureData.value.isEmpty) {
      signatureError.value = 'Tanda tangan tidak boleh kosong';
      isValid = false;
    }

    // Validate paraf
    if (parafData.value.isEmpty) {
      parafError.value = 'Paraf tidak boleh kosong';
      isValid = false;
    }

    return isValid;
  }

  // Check if form can be submitted
  bool canSubmitForm() {
    return isAllTermsRead.value &&
        isAgreementChecked.value &&
        isSignatureCompleted.value &&
        isParafCompleted.value;
  }

  @override
  void onClose() {
    // Clean up any resources if needed
    super.onClose();
  }
}
