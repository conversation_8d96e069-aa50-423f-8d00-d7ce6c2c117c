import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_terms_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormTermsSignature extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormTermsSignature({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final termsController = controller.termsController;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Tanda Tangan Perjanjian Keagenan'),
        _spjCard(
          context,
          onTap: () {
            // Navigate to PKAJ document viewer
            termsController.markPkajDocumentViewed();
          },
          isViewed: termsController.isPkajDocumentViewed,
        ),

        _padding(
          Text(
            'Paraf dan <PERSON>',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        // Warning when not all TNC are checked
        _padding(
          Obx(
            () =>
                termsController.isAllTermsRead.value
                    ? SizedBox.shrink()
                    : Container(
                      width: Get.width,
                      padding: EdgeInsets.all(paddingMedium),
                      decoration: BoxDecoration(
                        color: kColorGlobalBgRed,
                        borderRadius: BorderRadius.circular(radiusSmall),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: kColorGlobalRed,
                          ),
                          SizedBox(width: paddingSmall),
                          Expanded(
                            child: Text(
                              'Mohon membaca seluruh dokumen diatas sebelum menandatangani dokumen',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: kColorGlobalRed),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        ),
        // Paraf & ttd muncul ketika all the TNC are checked
        _padding(
          Obx(
            () =>
                termsController.isAllTermsRead.value
                    ? SizedBox(
                      width: Get.width,
                      child: Row(
                        children: [
                          _parafCard(context, termsController),
                          SizedBox(width: paddingMedium),
                          _signatureCard(context, termsController),
                        ],
                      ),
                    )
                    : SizedBox.shrink(),
          ),
        ),
        _padding(
          Obx(
            () => GestureDetector(
              onTap:
                  () => termsController.toggleAgreement(
                    !termsController.isAgreementChecked.value,
                  ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    termsController.isAgreementChecked.value
                        ? Icons.check_box_rounded
                        : Icons.check_box_outline_blank_rounded,
                    color:
                        termsController.isAgreementChecked.value
                            ? kColorGlobalBlue
                            : (Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Text(
                      'Dengan menandatangani dokumen berikut, saya (kandidat) telah membaca dan menyetujui seluruh dokumen perjanjian keagenan ini.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            Get.isDarkMode
                                ? kColorTextTersier
                                : kColorTextTersierLight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        _padding(Divider()),
        _padding(
          Text(
            'Anda dapat membagikan halaman ini untuk ditandatangani secara mandiri oleh kandidat.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
        ),
        _padding(
          SizedBox(
            width: Get.width,
            child: Obx(
              () => PdlButton(
                title: 'Bagikan',
                onPressed:
                    termsController.isSharing.value
                        ? null
                        : () => termsController.shareForm(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Padding _spjCard(
    BuildContext context, {
    required Function() onTap,
    required RxBool isViewed,
  }) {
    return _padding(
      Obx(
        () => GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  isViewed.isTrue
                      ? Icons.check_circle
                      : Icons.check_circle_outline,
                  color:
                      isViewed.isTrue
                          ? kColorGlobalGreen
                          : (Get.isDarkMode
                              ? kColorBorderDark
                              : kColorBorderLight),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    'Perjanjian Keagenan Asuransi Jiwa (PKAJ)',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                SizedBox(width: paddingSmall),
                Icon(Icons.chevron_right),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }

  Expanded _parafCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Paraf',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isParafCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  // Navigate to signature pad for paraf
                  // You can implement signature pad navigation here
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(radiusMedium),
                    border: Border.all(
                      color:
                          termsController.parafError.value.isNotEmpty
                              ? kColorGlobalRed
                              : (Get.isDarkMode
                                  ? kColorBorderDark
                                  : kColorBorderLight),
                    ),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isParafCompleted.value
                            ? Center(
                              child: Text(
                                'Paraf\nTersimpan',
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: kColorGlobalGreen),
                              ),
                            )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.parafError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.parafError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _signatureCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Tanda Tangan',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isSignatureCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  // Navigate to signature pad for signature
                  // You can implement signature pad navigation here
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(radiusMedium),
                    border: Border.all(
                      color:
                          termsController.signatureError.value.isNotEmpty
                              ? kColorGlobalRed
                              : (Get.isDarkMode
                                  ? kColorBorderDark
                                  : kColorBorderLight),
                    ),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isSignatureCompleted.value
                            ? Center(
                              child: Text(
                                'Tanda Tangan\nTersimpan',
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: kColorGlobalGreen),
                              ),
                            )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.signatureError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.signatureError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
